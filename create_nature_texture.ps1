# PowerShell script to create a simple nature-themed texture
# This creates a basic colored pattern for the phantom

# Create a simple 8x8 pattern that represents natural colors
# We'll use this as a base pattern

$pattern = @"
# Simple nature phantom texture pattern (8x8 base)
# G = Forest Green (#2D5016)
# D = Dark Green (#1A3009) 
# Y = Golden (#D4AF37)
# B = <PERSON><PERSON> (#8B4513)
# M = <PERSON> (#9ACD32)
# E = Gold Eyes (#FFD700)
# L = Leaf Green (#228B22)
# T = Transparent

BBBBBBBB
BGGGGGGB
BGEGGEGB
BGMGGMGB
BGGGGGGB
BGLLLGLB
BGYYYGYB
BBBBBBBB
"@

Write-Host "Nature Phantom Texture Pattern:"
Write-Host $pattern

Write-Host "`nColor Palette:"
Write-Host "G = <PERSON> Green (#2D5016) - Main body color"
Write-Host "D = Dark Green (#1A3009) - Shadow areas"
Write-Host "Y = Golden (#D4AF37) - Accent details"
Write-Host "B = <PERSON><PERSON> (#8B4513) - Outline/structure"
Write-Host "M = <PERSON> (#9ACD32) - Natural details"
Write-Host "E = Gold Eyes (#FFD700) - Glowing eyes"
Write-Host "L = Leaf Green (#228B22) - Wing details"

Write-Host "`nThis pattern should be scaled up to 64x64 pixels for the final texture."
Write-Host "Each character represents a 8x8 pixel block in the final 64x64 texture."

# Create a simple HTML file to visualize the colors
$html = @"
<!DOCTYPE html>
<html>
<head>
    <title>Nature Phantom Color Palette</title>
    <style>
        .color-box { width: 50px; height: 50px; display: inline-block; margin: 5px; border: 1px solid black; }
        .forest-green { background-color: #2D5016; }
        .dark-green { background-color: #1A3009; }
        .golden { background-color: #D4AF37; }
        .bark-brown { background-color: #8B4513; }
        .moss { background-color: #9ACD32; }
        .gold-eyes { background-color: #FFD700; }
        .leaf-green { background-color: #228B22; }
    </style>
</head>
<body>
    <h1>Nature Phantom Color Palette</h1>
    <div class="color-box forest-green" title="Forest Green #2D5016"></div>
    <div class="color-box dark-green" title="Dark Green #1A3009"></div>
    <div class="color-box golden" title="Golden #D4AF37"></div>
    <div class="color-box bark-brown" title="Bark Brown #8B4513"></div>
    <div class="color-box moss" title="Moss #9ACD32"></div>
    <div class="color-box gold-eyes" title="Gold Eyes #FFD700"></div>
    <div class="color-box leaf-green" title="Leaf Green #228B22"></div>
</body>
</html>
"@

$html | Out-File -FilePath "nature_phantom_colors.html" -Encoding UTF8
Write-Host "`nColor palette HTML file created: nature_phantom_colors.html"
Write-Host "Open this file in a web browser to see the color palette."
