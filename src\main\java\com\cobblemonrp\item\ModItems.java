package com.cobblemonrp.item;

import com.cobblemonrp.MobALaCon;
import net.fabricmc.fabric.api.item.v1.FabricItemSettings;
import net.fabricmc.fabric.api.itemgroup.v1.ItemGroupEvents;
import net.minecraft.item.Item;
import net.minecraft.item.ItemGroups;
import net.minecraft.item.SpawnEggItem;
import net.minecraft.registry.Registries;
import net.minecraft.registry.Registry;
import net.minecraft.util.Identifier;

public class ModItems {
    
    // Nature Phantom Spawn Egg (updated with natural colors)
    public static final Item SHADOW_PHANTOM_SPAWN_EGG = registerItem("shadow_phantom_spawn_egg",
        new SpawnEggItem(MobALaCon.SHADOW_PHANTOM, 0x2D5016, 0xD4AF37, new FabricItemSettings()));

    private static Item registerItem(String name, Item item) {
        return Registry.register(Registries.ITEM, new Identifier(MobALaCon.MOD_ID, name), item);
    }

    public static void registerModItems() {
        MobALaCon.LOGGER.info("Registering Mod Items for " + MobALaCon.MOD_ID);

        // Add spawn egg to creative inventory
        ItemGroupEvents.modifyEntriesEvent(ItemGroups.SPAWN_EGGS).register(entries -> {
            entries.add(SHADOW_PHANTOM_SPAWN_EGG);
        });
    }
}
