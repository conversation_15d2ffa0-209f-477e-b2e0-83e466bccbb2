package com.cobblemonrp.entity;

import net.minecraft.entity.EntityType;
import net.minecraft.entity.LivingEntity;
import net.minecraft.entity.ai.goal.*;
import net.minecraft.entity.ai.pathing.BirdNavigation;
import net.minecraft.entity.ai.pathing.EntityNavigation;
import net.minecraft.entity.attribute.DefaultAttributeContainer;
import net.minecraft.entity.attribute.EntityAttributes;
import net.minecraft.entity.damage.DamageSource;
import net.minecraft.entity.mob.HostileEntity;
import net.minecraft.entity.mob.PathAwareEntity;
import net.minecraft.entity.player.PlayerEntity;
import net.minecraft.sound.SoundEvent;
import net.minecraft.sound.SoundEvents;
import net.minecraft.util.math.Vec3d;
import net.minecraft.world.World;

/**
 * ShadowPhantom - A custom mob based on Phantom behavior but without day/night restrictions
 * This entity can attack players at any time of day, unlike the vanilla Phantom
 */
public class ShadowPhantomEntity extends HostileEntity {
    
    public ShadowPhantomEntity(EntityType<? extends ShadowPhantomEntity> entityType, World world) {
        super(entityType, world);
        this.experiencePoints = 5;
    }

    /**
     * Create attributes for the ShadowPhantom
     * Similar to Phantom but with some modifications
     */
    public static DefaultAttributeContainer.Builder createShadowPhantomAttributes() {
        return HostileEntity.createHostileAttributes()
            .add(EntityAttributes.GENERIC_MAX_HEALTH, 20.0) // Same as Phantom
            .add(EntityAttributes.GENERIC_ATTACK_DAMAGE, 2.0) // Same as Phantom
            .add(EntityAttributes.GENERIC_MOVEMENT_SPEED, 0.4) // Slightly faster than Phantom
            .add(EntityAttributes.GENERIC_FOLLOW_RANGE, 64.0) // Large follow range like Phantom
            .add(EntityAttributes.GENERIC_FLYING_SPEED, 0.6); // Flying speed
    }

    @Override
    protected void initGoals() {
        // Add AI goals similar to Phantom but without day/night restrictions
        this.goalSelector.add(1, new ShadowPhantomSweepAttackGoal());
        this.goalSelector.add(2, new ShadowPhantomCircleAroundAnchorGoal());
        this.goalSelector.add(3, new LookAtEntityGoal(this, PlayerEntity.class, 64.0f));
        
        // Target goals - attack players without day/night restrictions
        this.targetSelector.add(1, new ActiveTargetGoal<>(this, PlayerEntity.class, true));
    }

    @Override
    protected EntityNavigation createNavigation(World world) {
        BirdNavigation birdNavigation = new BirdNavigation(this, world);
        birdNavigation.setCanPathThroughDoors(false);
        birdNavigation.setCanSwim(false);
        birdNavigation.setCanEnterOpenDoors(true);
        return birdNavigation;
    }

    @Override
    public boolean canImmediatelyDespawn(double distanceSquared) {
        return false; // Don't despawn immediately like Phantoms
    }

    @Override
    protected SoundEvent getAmbientSound() {
        return SoundEvents.ENTITY_PHANTOM_AMBIENT;
    }

    @Override
    protected SoundEvent getHurtSound(DamageSource damageSource) {
        return SoundEvents.ENTITY_PHANTOM_HURT;
    }

    @Override
    protected SoundEvent getDeathSound() {
        return SoundEvents.ENTITY_PHANTOM_DEATH;
    }

    // Flying capability is handled by BirdNavigation

    @Override
    protected boolean isDisallowedInPeaceful() {
        return true; // Hostile mob, disappears in peaceful
    }

    /**
     * Custom sweep attack goal for ShadowPhantom
     * Based on Phantom's sweep attack but without day/night checks
     */
    class ShadowPhantomSweepAttackGoal extends Goal {
        private static final int SWEEP_COOLDOWN = 20;
        private int cooldown = SWEEP_COOLDOWN;

        @Override
        public boolean canStart() {
            LivingEntity target = ShadowPhantomEntity.this.getTarget();
            return target != null && ShadowPhantomEntity.this.canTarget(target);
        }

        @Override
        public boolean shouldContinue() {
            LivingEntity target = ShadowPhantomEntity.this.getTarget();
            if (target == null) {
                return false;
            }
            if (!ShadowPhantomEntity.this.canTarget(target)) {
                return false;
            }
            return true;
        }

        @Override
        public void tick() {
            LivingEntity target = ShadowPhantomEntity.this.getTarget();
            if (target == null) {
                return;
            }

            if (this.cooldown > 0) {
                --this.cooldown;
                return;
            }

            // Perform sweep attack
            Vec3d targetPos = target.getPos();
            Vec3d phantomPos = ShadowPhantomEntity.this.getPos();
            
            if (phantomPos.distanceTo(targetPos) < 1.5) {
                // Close enough to attack
                ShadowPhantomEntity.this.tryAttack(target);
                this.cooldown = SWEEP_COOLDOWN;
            } else {
                // Move towards target
                Vec3d direction = targetPos.subtract(phantomPos).normalize();
                ShadowPhantomEntity.this.setVelocity(direction.multiply(0.5));
            }
        }
    }

    /**
     * Custom circle around anchor goal for ShadowPhantom
     * Similar to Phantom's circling behavior
     */
    class ShadowPhantomCircleAroundAnchorGoal extends Goal {
        private float angle;
        private float radius;
        private int direction;

        @Override
        public boolean canStart() {
            LivingEntity target = ShadowPhantomEntity.this.getTarget();
            return target == null || ShadowPhantomEntity.this.distanceTo(target) > 9.0f;
        }

        @Override
        public void start() {
            this.radius = 5.0f + ShadowPhantomEntity.this.getRandom().nextFloat() * 10.0f;
            this.direction = ShadowPhantomEntity.this.getRandom().nextBoolean() ? 1 : -1;
            this.angle = ShadowPhantomEntity.this.getRandom().nextFloat() * 360.0f;
        }

        @Override
        public void tick() {
            this.angle += this.direction * 15.0f;
            
            LivingEntity target = ShadowPhantomEntity.this.getTarget();
            Vec3d anchorPos;
            
            if (target != null) {
                anchorPos = target.getPos();
            } else {
                anchorPos = ShadowPhantomEntity.this.getPos();
            }

            double x = anchorPos.x + this.radius * Math.cos(Math.toRadians(this.angle));
            double z = anchorPos.z + this.radius * Math.sin(Math.toRadians(this.angle));
            double y = anchorPos.y + 10.0 + ShadowPhantomEntity.this.getRandom().nextFloat() * 5.0;

            Vec3d targetPos = new Vec3d(x, y, z);
            Vec3d currentPos = ShadowPhantomEntity.this.getPos();
            Vec3d direction = targetPos.subtract(currentPos).normalize();

            ShadowPhantomEntity.this.setVelocity(direction.multiply(0.3));
        }
    }
}
