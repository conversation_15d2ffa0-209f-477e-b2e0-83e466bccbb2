#!/usr/bin/env python3
"""
Create a colored nature-themed texture for the Nature Phantom
"""

# Simple approach - create texture data as bytes
def create_nature_phantom_texture():
    """Create a 64x64 nature-themed texture"""
    
    # Define colors (RGB values)
    colors = {
        'bark': (139, 69, 19),      # Bar<PERSON> brown #8B4513
        'forest': (45, 80, 22),     # Forest green #2D5016  
        'gold': (255, 215, 0),      # Gold eyes #FFD700
        'moss': (154, 205, 50),     # Moss #9ACD32
        'leaf': (34, 139, 34),      # Leaf green #228B22
        'golden': (212, 175, 55),   # Golden accent #D4AF37
        'dark': (26, 48, 9),        # Dark green #1A3009
        'sage': (143, 188, 143),    # Sage green #8FBC8F
        'transparent': (0, 0, 0, 0) # Transparent
    }
    
    # Create a simple 16x16 pattern that will be repeated
    pattern = [
        'BBBBBBBBBBBBBBBB',
        'BFFFFFFFFFFFFFF B',
        'BFGFFFFFFFFFGFB',
        'BFMFFFFFFFFFMFB', 
        'BFFFFFFFFFFFFFF B',
        'BFFFFFFFFFFFFFF B',
        'BFLLLLLLLLLLLLFB',
        'BFYYYYYYYYYYYYFB',
        'BFFFFFFFFFFFFFF B',
        'BFFFFFFFFFFFFFF B',
        'BFLLLLLLLLLLLLFB',
        'BFYYYYYYYYYYYYFB',
        'BFFFFFFFFFFFFFF B',
        'BFGFFFFFFFFFGFB',
        'BFFFFFFFFFFFFFF B',
        'BBBBBBBBBBBBBBBB'
    ]
    
    # Map pattern characters to colors
    color_map = {
        'B': colors['bark'],
        'F': colors['forest'], 
        'G': colors['gold'],
        'M': colors['moss'],
        'L': colors['leaf'],
        'Y': colors['golden'],
        'D': colors['dark'],
        'S': colors['sage'],
        ' ': colors['transparent']
    }
    
    # Create 64x64 texture by scaling 16x16 pattern
    texture_data = []
    
    for row in range(64):
        pattern_row = row // 4  # Scale factor of 4
        for col in range(64):
            pattern_col = col // 4
            
            if pattern_row < len(pattern) and pattern_col < len(pattern[pattern_row]):
                char = pattern[pattern_row][pattern_col]
                color = color_map.get(char, colors['forest'])
                
                # Add RGB values
                if len(color) == 3:  # RGB
                    texture_data.extend([color[0], color[1], color[2], 255])  # Add alpha
                else:  # RGBA
                    texture_data.extend(color)
            else:
                # Default to transparent
                texture_data.extend([0, 0, 0, 0])
    
    return bytes(texture_data)

def write_simple_png(width, height, rgba_data, filename):
    """Write a simple PNG file"""
    import struct
    import zlib
    
    # PNG signature
    png_signature = b'\x89PNG\r\n\x1a\n'
    
    # Create IHDR chunk (image header)
    ihdr_data = struct.pack('>IIBBBBB', width, height, 8, 6, 0, 0, 0)  # 6 = RGBA
    ihdr_crc = zlib.crc32(b'IHDR' + ihdr_data) & 0xffffffff
    ihdr_chunk = struct.pack('>I', len(ihdr_data)) + b'IHDR' + ihdr_data + struct.pack('>I', ihdr_crc)
    
    # Prepare image data with filter bytes
    scanlines = b''
    for y in range(height):
        scanlines += b'\x00'  # Filter type 0 (None)
        start = y * width * 4
        end = start + width * 4
        scanlines += rgba_data[start:end]
    
    # Compress image data
    compressed_data = zlib.compress(scanlines)
    
    # Create IDAT chunk (image data)
    idat_crc = zlib.crc32(b'IDAT' + compressed_data) & 0xffffffff
    idat_chunk = struct.pack('>I', len(compressed_data)) + b'IDAT' + compressed_data + struct.pack('>I', idat_crc)
    
    # Create IEND chunk (image end)
    iend_crc = zlib.crc32(b'IEND') & 0xffffffff
    iend_chunk = struct.pack('>I', 0) + b'IEND' + struct.pack('>I', iend_crc)
    
    # Write PNG file
    with open(filename, 'wb') as f:
        f.write(png_signature + ihdr_chunk + idat_chunk + iend_chunk)

if __name__ == "__main__":
    try:
        print("Creating nature-themed texture...")
        texture_data = create_nature_phantom_texture()
        
        output_file = "src/main/resources/assets/mob-a-la-con/textures/entity/shadow_phantom.png"
        write_simple_png(64, 64, texture_data, output_file)
        
        print(f"✅ Nature texture created successfully: {output_file}")
        print("Colors used:")
        print("  🟤 Bark Brown - Structure and outline")
        print("  🟢 Forest Green - Main body color")
        print("  🟡 Gold - Eyes and highlights")
        print("  🌿 Moss Green - Natural details")
        print("  🍃 Leaf Green - Wing details")
        print("  ✨ Golden - Accent colors")
        
    except Exception as e:
        print(f"❌ Error creating texture: {e}")
        print("Falling back to manual creation...")
