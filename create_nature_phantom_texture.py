#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to create a nature-themed phantom texture for Minecraft
Creates a 64x64 PNG texture with forest colors and organic patterns
"""

from PIL import Image, ImageDraw
import numpy as np

def create_nature_phantom_texture():
    # Create 64x64 image with transparency
    img = Image.new('RGBA', (64, 64), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # Color palette
    colors = {
        'forest_green': (45, 80, 22, 255),      # #2D5016
        'dark_green': (26, 48, 9, 255),         # #1A3009
        'golden': (212, 175, 55, 255),          # #D4AF37
        'sage_green': (143, 188, 143, 255),     # #8FBC8F
        'shadow': (15, 26, 10, 255),            # #0F1A0A
        'bark': (139, 69, 19, 255),             # #8B4513
        'moss': (154, 205, 50, 255),            # #9ACD32
        'gold_glow': (255, 215, 0, 255),        # #FFD700
        'leaf_green': (34, 139, 34, 255),       # #228B22
        'vine_green': (85, 107, 47, 255),       # #556B2F
    }
    
    # Head area (0, 0) - 7x3x5 pixels
    # Draw head base
    for x in range(0, 7):
        for y in range(0, 5):
            if x == 0 or x == 6 or y == 0 or y == 4:
                img.putpixel((x, y), colors['bark'])
            else:
                img.putpixel((x, y), colors['forest_green'])
    
    # Add moss details to head
    img.putpixel((2, 2), colors['moss'])
    img.putpixel((4, 2), colors['moss'])
    
    # Eyes (glowing golden)
    img.putpixel((1, 1), colors['gold_glow'])
    img.putpixel((5, 1), colors['gold_glow'])
    
    # Head sides
    for y in range(5, 8):
        for x in range(0, 7):
            if x == 0 or x == 6:
                img.putpixel((x, y), colors['dark_green'])
            else:
                img.putpixel((x, y), colors['sage_green'])
    
    # Body area (0, 8) - 5x3x9 pixels
    # Draw body base
    for x in range(0, 5):
        for y in range(8, 17):
            if x == 0 or x == 4 or y == 8 or y == 16:
                img.putpixel((x, y), colors['bark'])
            else:
                img.putpixel((x, y), colors['forest_green'])
    
    # Add vine patterns to body
    img.putpixel((1, 10), colors['vine_green'])
    img.putpixel((2, 12), colors['vine_green'])
    img.putpixel((3, 14), colors['vine_green'])
    
    # Body sides
    for y in range(8, 17):
        for x in range(5, 10):
            img.putpixel((x, y), colors['sage_green'])
    
    # Wing Base area (23, 12) - 6x2x9 pixels
    # Left wing base
    for x in range(23, 29):
        for y in range(12, 21):
            if (x + y) % 2 == 0:  # Checkered pattern for leaf texture
                img.putpixel((x, y), colors['leaf_green'])
            else:
                img.putpixel((x, y), colors['forest_green'])
    
    # Wing base sides
    for y in range(12, 21):
        for x in range(29, 31):
            img.putpixel((x, y), colors['dark_green'])
    
    # Wing Tip area (16, 24) - 13x1x9 pixels
    # Create leaf-like wing tips with transparency
    for x in range(16, 29):
        for y in range(24, 33):
            # Create leaf pattern with varying transparency
            if (x - 16) % 3 == 0 and (y - 24) % 2 == 0:
                # Leaf veins
                img.putpixel((x, y), colors['golden'])
            elif (x + y) % 2 == 0:
                # Semi-transparent leaf areas
                color = list(colors['leaf_green'])
                color[3] = 180  # Semi-transparent
                img.putpixel((x, y), tuple(color))
            else:
                # More transparent areas
                color = list(colors['sage_green'])
                color[3] = 120  # More transparent
                img.putpixel((x, y), tuple(color))
    
    # Tail area (3, 20) - 3x2x6 pixels
    for x in range(3, 6):
        for y in range(20, 26):
            if x == 3 or x == 5:
                img.putpixel((x, y), colors['bark'])
            else:
                img.putpixel((x, y), colors['vine_green'])
    
    # Tail2 area (4, 29) - 1x1x6 pixels
    for y in range(29, 35):
        img.putpixel((4, y), colors['moss'])
    
    # Add some scattered moss and golden accents throughout
    accent_positions = [
        (10, 5), (15, 8), (20, 15), (35, 20), (40, 25),
        (12, 30), (25, 35), (30, 40), (45, 10), (50, 18)
    ]
    
    for x, y in accent_positions:
        if x < 64 and y < 64:
            if (x + y) % 3 == 0:
                img.putpixel((x, y), colors['golden'])
            else:
                img.putpixel((x, y), colors['moss'])
    
    # Add some bark texture details
    bark_positions = [
        (8, 12), (18, 16), (28, 22), (38, 28), (48, 35)
    ]
    
    for x, y in bark_positions:
        if x < 64 and y < 64:
            img.putpixel((x, y), colors['bark'])
    
    return img

if __name__ == "__main__":
    # Create the texture
    texture = create_nature_phantom_texture()
    
    # Save the texture
    texture.save("src/main/resources/assets/mob-a-la-con/textures/entity/shadow_phantom.png")
    print("Nature phantom texture created successfully!")
    print("Saved as: src/main/resources/assets/mob-a-la-con/textures/entity/shadow_phantom.png")
