# Create a Minecraft-style Nature Phantom texture
Add-Type -AssemblyName System.Drawing

Write-Host "Creating Minecraft-style Nature Phantom texture..." -ForegroundColor Green

try {
    # Create a 64x64 bitmap
    $bitmap = New-Object System.Drawing.Bitmap(64, 64)
    $graphics = [System.Drawing.Graphics]::FromImage($bitmap)
    
    # Define nature colors
    $forestGreen = [System.Drawing.Color]::FromArgb(45, 80, 22)        # #2D5016
    $darkGreen = [System.Drawing.Color]::FromArgb(26, 48, 9)           # #1A3009
    $goldenYellow = [System.Drawing.Color]::FromArgb(212, 175, 55)     # #D4AF37
    $sageGreen = [System.Drawing.Color]::FromArgb(143, 188, 143)       # #8FBC8F
    $barkBrown = [System.Drawing.Color]::FromArgb(139, 69, 19)         # #8B4513
    $mossGreen = [System.Drawing.Color]::FromArgb(154, 205, 50)        # #9ACD32
    $goldGlow = [System.Drawing.Color]::FromArgb(255, 215, 0)          # #FFD700
    $transparent = [System.Drawing.Color]::FromArgb(0, 0, 0, 0)
    
    # Clear background
    $graphics.Clear($transparent)
    
    # Helper function to set pixel
    function SetPixel($x, $y, $color) {
        if ($x -ge 0 -and $x -lt 64 -and $y -ge 0 -and $y -lt 64) {
            $bitmap.SetPixel($x, $y, $color)
        }
    }
    
    # === HEAD SECTION (Phantom style) ===
    # Head top (0,0 to 7,5)
    for ($x = 0; $x -lt 7; $x++) {
        for ($y = 0; $y -lt 5; $y++) {
            if ($x -eq 0 -or $x -eq 6 -or $y -eq 0 -or $y -eq 4) {
                SetPixel $x $y $darkGreen
            } else {
                SetPixel $x $y $forestGreen
            }
        }
    }
    
    # Head front (7,0 to 12,5) - with eyes
    for ($x = 7; $x -lt 12; $x++) {
        for ($y = 0; $y -lt 5; $y++) {
            if ($x -eq 7 -or $x -eq 11 -or $y -eq 0 -or $y -eq 4) {
                SetPixel $x $y $darkGreen
            } elseif (($x -eq 8 -and $y -eq 1) -or ($x -eq 10 -and $y -eq 1)) {
                SetPixel $x $y $goldGlow  # Eyes
            } else {
                SetPixel $x $y $forestGreen
            }
        }
    }
    
    # Head right (12,0 to 19,5)
    for ($x = 12; $x -lt 19; $x++) {
        for ($y = 0; $y -lt 5; $y++) {
            if ($x -eq 12 -or $x -eq 18 -or $y -eq 0 -or $y -eq 4) {
                SetPixel $x $y $darkGreen
            } else {
                SetPixel $x $y $forestGreen
            }
        }
    }
    
    # Head left (19,0 to 26,5)
    for ($x = 19; $x -lt 26; $x++) {
        for ($y = 0; $y -lt 5; $y++) {
            if ($x -eq 19 -or $x -eq 25 -or $y -eq 0 -or $y -eq 4) {
                SetPixel $x $y $darkGreen
            } else {
                SetPixel $x $y $forestGreen
            }
        }
    }
    
    # Head back (26,0 to 31,5)
    for ($x = 26; $x -lt 31; $x++) {
        for ($y = 0; $y -lt 5; $y++) {
            SetPixel $x $y $darkGreen
        }
    }
    
    # Head bottom (31,0 to 38,5)
    for ($x = 31; $x -lt 38; $x++) {
        for ($y = 0; $y -lt 5; $y++) {
            SetPixel $x $y $sageGreen
        }
    }
    
    # === BODY SECTION ===
    # Body top (0,8 to 9,11)
    for ($x = 0; $x -lt 9; $x++) {
        for ($y = 8; $y -lt 11; $y++) {
            if ($x -eq 0 -or $x -eq 8 -or $y -eq 8 -or $y -eq 10) {
                SetPixel $x $y $darkGreen
            } elseif ($y -eq 8) {
                SetPixel $x $y $mossGreen  # Moss stripe
            } else {
                SetPixel $x $y $forestGreen
            }
        }
    }
    
    # Body front (9,8 to 14,11)
    for ($x = 9; $x -lt 14; $x++) {
        for ($y = 8; $y -lt 11; $y++) {
            if ($x -eq 9 -or $x -eq 13 -or $y -eq 8 -or $y -eq 10) {
                SetPixel $x $y $darkGreen
            } elseif ($y -eq 9 -and $x -ge 10 -and $x -le 12) {
                SetPixel $x $y $barkBrown  # Bark detail
            } else {
                SetPixel $x $y $forestGreen
            }
        }
    }
    
    # Body sides and back
    for ($x = 14; $x -lt 37; $x++) {
        for ($y = 8; $y -lt 11; $y++) {
            if ($x -lt 23) {
                # Right side
                if ($x -eq 14 -or $x -eq 22 -or $y -eq 8 -or $y -eq 10) {
                    SetPixel $x $y $darkGreen
                } else {
                    SetPixel $x $y $forestGreen
                }
            } elseif ($x -lt 32) {
                # Left side
                if ($x -eq 23 -or $x -eq 31 -or $y -eq 8 -or $y -eq 10) {
                    SetPixel $x $y $darkGreen
                } else {
                    SetPixel $x $y $forestGreen
                }
            } else {
                # Back
                SetPixel $x $y $darkGreen
            }
        }
    }
    
    # Body bottom (37,8 to 46,11)
    for ($x = 37; $x -lt 46; $x++) {
        for ($y = 8; $y -lt 11; $y++) {
            SetPixel $x $y $sageGreen
        }
    }
    
    # === WING SECTIONS ===
    # Wing base (23,12 to 32,14)
    for ($x = 23; $x -lt 32; $x++) {
        for ($y = 12; $y -lt 14; $y++) {
            if ($x -eq 23 -or $x -eq 31 -or $y -eq 12 -or $y -eq 13) {
                SetPixel $x $y $darkGreen
            } elseif ($y -eq 12) {
                SetPixel $x $y $goldenYellow  # Golden accent
            } else {
                SetPixel $x $y $forestGreen
            }
        }
    }
    
    # Wing base front (32,12 to 38,14)
    for ($x = 32; $x -lt 38; $x++) {
        for ($y = 12; $y -lt 14; $y++) {
            if ($x -eq 32 -or $x -eq 37 -or $y -eq 12 -or $y -eq 13) {
                SetPixel $x $y $darkGreen
            } else {
                SetPixel $x $y $forestGreen
            }
        }
    }
    
    # Wing membranes with leaf pattern
    # Left wing (0,16 to 15,23)
    for ($x = 0; $x -lt 16; $x++) {
        for ($y = 16; $y -lt 24; $y++) {
            if (($x + $y) % 3 -eq 0) {
                SetPixel $x $y $sageGreen
            } elseif (($x + $y) % 5 -eq 0) {
                SetPixel $x $y $goldenYellow
            } elseif (($x + $y) % 4 -eq 0) {
                SetPixel $x $y $mossGreen
            } else {
                SetPixel $x $y $forestGreen
            }
        }
    }
    
    # Right wing (32,16 to 47,23)
    for ($x = 32; $x -lt 48; $x++) {
        for ($y = 16; $y -lt 24; $y++) {
            if (($x + $y) % 3 -eq 0) {
                SetPixel $x $y $sageGreen
            } elseif (($x + $y) % 5 -eq 0) {
                SetPixel $x $y $goldenYellow
            } elseif (($x + $y) % 4 -eq 0) {
                SetPixel $x $y $mossGreen
            } else {
                SetPixel $x $y $forestGreen
            }
        }
    }
    
    # Wing tips (16,24 to 29,25)
    for ($x = 16; $x -lt 29; $x++) {
        if ($x % 2 -eq 0) {
            SetPixel $x 24 $goldenYellow
        } else {
            SetPixel $x 24 $sageGreen
        }
    }
    
    # === TAIL SECTIONS ===
    # Tail (3,20 to 9,22)
    for ($x = 3; $x -lt 9; $x++) {
        for ($y = 20; $y -lt 22; $y++) {
            if ($x -eq 3 -or $x -eq 8 -or $y -eq 20 -or $y -eq 21) {
                SetPixel $x $y $darkGreen
            } elseif ($y -eq 20) {
                SetPixel $x $y $mossGreen
            } else {
                SetPixel $x $y $forestGreen
            }
        }
    }
    
    # Tail2 (4,29 to 10,30)
    for ($x = 4; $x -lt 10; $x++) {
        if ($x % 2 -eq 1) {
            SetPixel $x 29 $goldenYellow
        } else {
            SetPixel $x 29 $darkGreen
        }
    }
    
    # Save as PNG
    $outputPath = "src\main\resources\assets\mob-a-la-con\textures\entity\shadow_phantom.png"
    $bitmap.Save($outputPath, [System.Drawing.Imaging.ImageFormat]::Png)
    
    Write-Host "Minecraft-style Nature Phantom texture created successfully!" -ForegroundColor Green
    Write-Host "Saved to: $outputPath" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "Features:" -ForegroundColor Yellow
    Write-Host "  - Proper Phantom UV mapping" -ForegroundColor Green
    Write-Host "  - Minecraft pixel art style" -ForegroundColor Green
    Write-Host "  - Nature color palette" -ForegroundColor Green
    Write-Host "  - Golden glowing eyes" -ForegroundColor Yellow
    Write-Host "  - Moss and bark details" -ForegroundColor Green
    Write-Host "  - Leaf-pattern wing membranes" -ForegroundColor Green
    
    # Cleanup
    $graphics.Dispose()
    $bitmap.Dispose()
    
} catch {
    Write-Host "Error creating texture: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Please check if the output directory exists." -ForegroundColor Yellow
}
