<!DOCTYPE html>
<html>
<head>
    <title>Nature Phantom Texture Creator</title>
    <style>
        canvas { border: 1px solid black; image-rendering: pixelated; }
        .controls { margin: 10px 0; }
        .color-palette { display: flex; gap: 10px; margin: 10px 0; }
        .color-box { width: 40px; height: 40px; border: 2px solid black; cursor: pointer; }
        .selected { border-color: red; border-width: 3px; }
    </style>
</head>
<body>
    <h1>Nature Phantom Texture Creator</h1>
    <p>Click on colors below, then click on the canvas to paint!</p>
    
    <div class="color-palette">
        <div class="color-box" style="background-color: #8B4513" data-color="#8B4513" title="Bark Brown"></div>
        <div class="color-box selected" style="background-color: #2D5016" data-color="#2D5016" title="Forest Green"></div>
        <div class="color-box" style="background-color: #FFD700" data-color="#FFD700" title="Gold Eyes"></div>
        <div class="color-box" style="background-color: #9ACD32" data-color="#9ACD32" title="Moss"></div>
        <div class="color-box" style="background-color: #228B22" data-color="#228B22" title="Leaf Green"></div>
        <div class="color-box" style="background-color: #D4AF37" data-color="#D4AF37" title="Golden"></div>
        <div class="color-box" style="background-color: #1A3009" data-color="#1A3009" title="Dark Green"></div>
        <div class="color-box" style="background-color: #8FBC8F" data-color="#8FBC8F" title="Sage Green"></div>
    </div>
    
    <div class="controls">
        <button onclick="createPattern()">Create Nature Pattern</button>
        <button onclick="clearCanvas()">Clear Canvas</button>
        <button onclick="downloadTexture()">Download PNG</button>
    </div>
    
    <canvas id="textureCanvas" width="64" height="64" style="width: 320px; height: 320px;"></canvas>
    
    <script>
        const canvas = document.getElementById('textureCanvas');
        const ctx = canvas.getContext('2d');
        let selectedColor = '#2D5016';
        
        // Color palette selection
        document.querySelectorAll('.color-box').forEach(box => {
            box.addEventListener('click', function() {
                document.querySelector('.color-box.selected').classList.remove('selected');
                this.classList.add('selected');
                selectedColor = this.dataset.color;
            });
        });
        
        // Canvas painting
        canvas.addEventListener('click', function(e) {
            const rect = canvas.getBoundingClientRect();
            const x = Math.floor((e.clientX - rect.left) * 64 / rect.width);
            const y = Math.floor((e.clientY - rect.top) * 64 / rect.height);
            
            ctx.fillStyle = selectedColor;
            ctx.fillRect(x, y, 1, 1);
        });
        
        function createPattern() {
            // Clear canvas
            ctx.clearRect(0, 0, 64, 64);
            
            // Define colors
            const colors = {
                'B': '#8B4513', // Bark brown
                'G': '#2D5016', // Forest green
                'E': '#FFD700', // Gold eyes
                'M': '#9ACD32', // Moss
                'L': '#228B22', // Leaf green
                'Y': '#D4AF37', // Golden
                'D': '#1A3009', // Dark green
                'S': '#8FBC8F'  // Sage green
            };
            
            // Simple 16x16 pattern (scaled to 64x64)
            const pattern = [
                'BBBBBBBBBBBBBBBB',
                'BGGGGGGGGGGGGGGB',
                'BGEGGGGGGGGGGEGB',
                'BGMGGGGGGGGGGMGB',
                'BGGGGGGGGGGGGGGB',
                'BGGGGGGGGGGGGGGB',
                'BGLLLLLLLLLLLGLB',
                'BGYYYYYYYYYYYYGB',
                'BGGGGGGGGGGGGGGB',
                'BGGGGGGGGGGGGGGB',
                'BGLLLLLLLLLLLGLB',
                'BGYYYYYYYYYYYYGB',
                'BGGGGGGGGGGGGGGB',
                'BGEGGGGGGGGGGEGB',
                'BGGGGGGGGGGGGGGB',
                'BBBBBBBBBBBBBBBB'
            ];
            
            // Draw pattern (each character becomes a 4x4 block)
            for (let y = 0; y < 16; y++) {
                for (let x = 0; x < 16; x++) {
                    const char = pattern[y][x];
                    if (colors[char]) {
                        ctx.fillStyle = colors[char];
                        ctx.fillRect(x * 4, y * 4, 4, 4);
                    }
                }
            }
        }
        
        function clearCanvas() {
            ctx.clearRect(0, 0, 64, 64);
        }
        
        function downloadTexture() {
            const link = document.createElement('a');
            link.download = 'shadow_phantom.png';
            link.href = canvas.toDataURL();
            link.click();
        }
        
        // Create initial pattern
        createPattern();
    </script>
</body>
</html>
