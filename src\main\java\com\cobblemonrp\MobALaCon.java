package com.cobblemonrp;

import net.fabricmc.api.ModInitializer;
import net.fabricmc.fabric.api.object.builder.v1.entity.FabricDefaultAttributeRegistry;
import net.minecraft.entity.EntityType;
import net.minecraft.entity.SpawnGroup;
import net.minecraft.registry.Registries;
import net.minecraft.registry.Registry;
import net.minecraft.util.Identifier;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.cobblemonrp.entity.ShadowPhantomEntity;
import com.cobblemonrp.item.ModItems;

public class MobALaCon implements ModInitializer {
	public static final String MOD_ID = "mob-a-la-con";

	// This logger is used to write text to the console and the log file.
	// It is considered best practice to use your mod id as the logger's name.
	// That way, it's clear which mod wrote info, warnings, and errors.
	public static final Logger LOGGER = LoggerFactory.getLogger(MOD_ID);

	// Register our ShadowPhantom entity
	public static final EntityType<ShadowPhantomEntity> SHADOW_PHANTOM = Registry.register(
		Registries.ENTITY_TYPE,
		new Identifier(MOD_ID, "shadow_phantom"),
		EntityType.Builder.create(ShadowPhantomEntity::new, SpawnGroup.MONSTER)
			.setDimensions(0.9f, 0.5f) // Similar to Phantom dimensions
			.maxTrackingRange(8)
			.build("shadow_phantom")
	);

	@Override
	public void onInitialize() {
		// This code runs as soon as Minecraft is in a mod-load-ready state.
		// However, some things (like resources) may still be uninitialized.
		// Proceed with mild caution.

		LOGGER.info("Initializing Mob A La Con!");

		// Register entity attributes
		FabricDefaultAttributeRegistry.register(SHADOW_PHANTOM, ShadowPhantomEntity.createShadowPhantomAttributes());

		// Register items (including spawn eggs)
		ModItems.registerModItems();

		LOGGER.info("Mob A La Con initialized successfully!");
	}
}