#!/usr/bin/env python3
"""
Simple script to create a nature phantom texture using basic image creation
"""

# Create a simple 64x64 texture with natural colors
# We'll create this as a simple pattern that can be saved as PNG

def create_simple_nature_texture():
    # Create a simple text-based representation that we can convert
    # This will be a 64x64 grid with color codes
    
    # Color mapping (simplified)
    colors = {
        'G': (45, 80, 22),      # <PERSON> green
        'D': (26, 48, 9),       # Dark green  
        'Y': (212, 175, 55),    # Golden
        'S': (143, 188, 143),   # <PERSON> green
        'B': (139, 69, 19),     # Bar<PERSON> brown
        'M': (154, 205, 50),    # Moss
        'E': (255, 215, 0),     # Gold glow (eyes)
        'L': (34, 139, 34),     # <PERSON> green
        'V': (85, 107, 47),     # <PERSON>e green
        'T': (0, 0, 0, 0),      # Transparent
    }
    
    # Create a simple pattern for the phantom
    # This is a simplified 16x16 pattern that we'll scale up
    pattern = [
        "TTTTBBBBBBTTTTTT",
        "TTTBGGGGGGBTTTTT", 
        "TTBGEEGGGEGBTTTT",
        "TTBGMGGGMGBTTTT",
        "TTBGGGGGGGBTTTT",
        "TTTBGGGGGGBTTTTT",
        "TTTTBBBBBBTTTTTT",
        "TTTBGGGGGGBTTTTT",
        "TTBGVGGGVGBTTTT",
        "TBGGGGGGGGGGBTT",
        "BGGGGGGGGGGGGB",
        "BGGGGGGGGGGGGB",
        "TBGGGGGGGGGGBTT",
        "TTBGGGGGGGBTTTT",
        "TTTBGGGGGBTTTTT",
        "TTTTBBBBBTTTTTT"
    ]
    
    # Wing pattern (simplified)
    wing_pattern = [
        "LLLLYYLLLLYYLLL",
        "LLLYYYLYYYLYYLL", 
        "LLYYYLYYLYYYLLL",
        "LYYYLYYLYYYLLLL",
        "YYYLYYLYYYLLLLL",
        "YYLYYLYYYLLLLLL",
        "YLYYLYYYLLLLLLL",
        "LYYLYYYLLLLLLLL",
        "YYLYYYLLLLLLLLL",
        "YLYYYLLLLLLLLLL",
        "LYYYLLLLLLLLLLL",
        "YYYLLLLLLLLLLLL",
        "YYLLLLLLLLLLLLL",
        "YLLLLLLLLLLLLLL",
        "LLLLLLLLLLLLLLL",
        "LLLLLLLLLLLLLLL"
    ]
    
    print("Creating nature phantom texture pattern...")
    print("Main body pattern:")
    for row in pattern:
        print(row)
    
    print("\nWing pattern:")
    for row in wing_pattern:
        print(row)
    
    print("\nColor legend:")
    print("G = Forest Green, D = Dark Green, Y = Golden")
    print("S = Sage Green, B = Bark Brown, M = Moss")
    print("E = Gold Glow (eyes), L = Leaf Green, V = Vine Green")
    print("T = Transparent")
    
    return pattern, wing_pattern, colors

if __name__ == "__main__":
    pattern, wing_pattern, colors = create_simple_nature_texture()
    print("\nTexture pattern created! You can use this as a reference to create the actual PNG file.")
    print("The pattern should be scaled up to 64x64 pixels for the final texture.")
