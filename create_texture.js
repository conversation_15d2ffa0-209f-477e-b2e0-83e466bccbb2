// <PERSON>ript to create a nature-themed phantom texture
// This creates a simple 64x64 texture with natural colors

const fs = require('fs');
const { createCanvas } = require('canvas');

function createNaturePhantomTexture() {
    const canvas = createCanvas(64, 64);
    const ctx = canvas.getContext('2d');
    
    // Clear canvas with transparency
    ctx.clearRect(0, 0, 64, 64);
    
    // Color palette
    const colors = {
        forestGreen: '#2D5016',
        darkGreen: '#1A3009',
        golden: '#D4AF37',
        sageGreen: '#8FBC8F',
        shadow: '#0F1A0A',
        bark: '#8B4513',
        moss: '#9ACD32',
        goldGlow: '#FFD700',
        leafGreen: '#228B22',
        vineGreen: '#556B2F'
    };
    
    // Fill background with forest green
    ctx.fillStyle = colors.forestGreen;
    ctx.fillRect(0, 0, 64, 64);
    
    // Head area (0, 0) - 7x5 pixels
    ctx.fillStyle = colors.bark;
    ctx.fillRect(0, 0, 7, 5);
    
    ctx.fillStyle = colors.forestGreen;
    ctx.fillRect(1, 1, 5, 3);
    
    // Eyes (glowing golden)
    ctx.fillStyle = colors.goldGlow;
    ctx.fillRect(1, 1, 1, 1);
    ctx.fillRect(5, 1, 1, 1);
    
    // Add moss details
    ctx.fillStyle = colors.moss;
    ctx.fillRect(2, 2, 1, 1);
    ctx.fillRect(4, 2, 1, 1);
    
    // Body area (0, 8) - 5x9 pixels
    ctx.fillStyle = colors.bark;
    ctx.fillRect(0, 8, 5, 9);
    
    ctx.fillStyle = colors.forestGreen;
    ctx.fillRect(1, 9, 3, 7);
    
    // Add vine patterns
    ctx.fillStyle = colors.vineGreen;
    ctx.fillRect(1, 10, 1, 1);
    ctx.fillRect(2, 12, 1, 1);
    ctx.fillRect(3, 14, 1, 1);
    
    // Wing areas with leaf patterns
    ctx.fillStyle = colors.leafGreen;
    ctx.fillRect(23, 12, 6, 9);
    ctx.fillRect(16, 24, 13, 9);
    
    // Add golden veins to wings
    ctx.fillStyle = colors.golden;
    for (let i = 0; i < 6; i += 2) {
        ctx.fillRect(23 + i, 12, 1, 9);
    }
    for (let i = 0; i < 13; i += 3) {
        ctx.fillRect(16 + i, 24, 1, 9);
    }
    
    // Tail areas
    ctx.fillStyle = colors.bark;
    ctx.fillRect(3, 20, 3, 6);
    
    ctx.fillStyle = colors.vineGreen;
    ctx.fillRect(4, 21, 1, 4);
    
    ctx.fillStyle = colors.moss;
    ctx.fillRect(4, 29, 1, 6);
    
    // Add scattered natural accents
    ctx.fillStyle = colors.golden;
    const accentPositions = [
        [10, 5], [15, 8], [20, 15], [35, 20], [40, 25],
        [12, 30], [25, 35], [30, 40], [45, 10], [50, 18]
    ];
    
    accentPositions.forEach(([x, y]) => {
        if (x < 64 && y < 64) {
            ctx.fillRect(x, y, 1, 1);
        }
    });
    
    // Add moss spots
    ctx.fillStyle = colors.moss;
    const mossPositions = [
        [8, 12], [18, 16], [28, 22], [38, 28], [48, 35],
        [5, 25], [15, 35], [25, 45], [35, 55], [45, 25]
    ];
    
    mossPositions.forEach(([x, y]) => {
        if (x < 64 && y < 64) {
            ctx.fillRect(x, y, 1, 1);
        }
    });
    
    return canvas;
}

// Create and save the texture
try {
    const canvas = createNaturePhantomTexture();
    const buffer = canvas.toBuffer('image/png');
    fs.writeFileSync('src/main/resources/assets/mob-a-la-con/textures/entity/shadow_phantom.png', buffer);
    console.log('Nature phantom texture created successfully!');
} catch (error) {
    console.error('Error creating texture:', error.message);
}
