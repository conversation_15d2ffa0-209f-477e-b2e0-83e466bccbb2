package com.cobblemonrp.client.renderer;

import com.cobblemonrp.MobALaCon;
import com.cobblemonrp.client.MobALaConClient;
import com.cobblemonrp.client.model.ShadowPhantomEntityModel;
import com.cobblemonrp.entity.ShadowPhantomEntity;
import net.minecraft.client.render.entity.EntityRendererFactory;
import net.minecraft.client.render.entity.MobEntityRenderer;
import net.minecraft.util.Identifier;

/**
 * Renderer for the ShadowPhantom entity
 */
public class ShadowPhantomEntityRenderer extends MobEntityRenderer<ShadowPhantomEntity, ShadowPhantomEntityModel> {

    private static final Identifier TEXTURE = new Identifier(MobALaCon.MOD_ID, "textures/entity/shadow_phantom.png");

    public ShadowPhantomEntityRenderer(EntityRendererFactory.Context context) {
        super(context, new ShadowPhantomEntityModel(context.getPart(MobALaConClient.SHADOW_PHANTOM_LAYER)), 0.75f);
    }

    @Override
    public Identifier getTexture(ShadowPhantomEntity entity) {
        return TEXTURE;
    }
}
