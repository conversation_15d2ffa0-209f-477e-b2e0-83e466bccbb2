#!/usr/bin/env python3
"""
Manual texture creation using basic file operations
Creates a simple nature-themed texture
"""

import struct

def create_simple_png():
    """Create a very simple 8x8 PNG with natural colors"""
    
    # PNG signature
    png_signature = b'\x89PNG\r\n\x1a\n'
    
    # Image data (8x8 pixels, RGB)
    width, height = 8, 8
    
    # Color palette (RGB values)
    colors = {
        'B': (139, 69, 19),    # Bark brown
        'G': (45, 80, 22),     # Forest green  
        'E': (255, 215, 0),    # Gold eyes
        'M': (154, 205, 50),   # Moss
        'L': (34, 139, 34),    # Leaf green
        'Y': (212, 175, 55),   # Golden
    }
    
    # Simple 8x8 pattern
    pattern = [
        'BBBBBBBB',
        'BGGGGGGB', 
        'BGEGGEGB',
        'BGMGGMGB',
        'BGGGGGGB',
        'BGLLLGLB',
        'BGYYYGYB',
        'BBBBBBBB'
    ]
    
    # Create pixel data
    pixel_data = []
    for row in pattern:
        for char in row:
            if char in colors:
                r, g, b = colors[char]
                pixel_data.extend([r, g, b])
            else:
                pixel_data.extend([0, 0, 0])  # Black for unknown
    
    # Convert to bytes
    raw_data = bytes(pixel_data)
    
    # Create scanlines (add filter byte 0 to each row)
    scanlines = b''
    for y in range(height):
        scanlines += b'\x00'  # Filter type 0 (None)
        start = y * width * 3
        end = start + width * 3
        scanlines += raw_data[start:end]
    
    # Compress the data (simple, no actual compression)
    import zlib
    compressed_data = zlib.compress(scanlines)
    
    # Create IHDR chunk
    ihdr_data = struct.pack('>IIBBBBB', width, height, 8, 2, 0, 0, 0)
    ihdr_crc = zlib.crc32(b'IHDR' + ihdr_data) & 0xffffffff
    ihdr_chunk = struct.pack('>I', len(ihdr_data)) + b'IHDR' + ihdr_data + struct.pack('>I', ihdr_crc)
    
    # Create IDAT chunk
    idat_crc = zlib.crc32(b'IDAT' + compressed_data) & 0xffffffff
    idat_chunk = struct.pack('>I', len(compressed_data)) + b'IDAT' + compressed_data + struct.pack('>I', idat_crc)
    
    # Create IEND chunk
    iend_crc = zlib.crc32(b'IEND') & 0xffffffff
    iend_chunk = struct.pack('>I', 0) + b'IEND' + struct.pack('>I', iend_crc)
    
    # Combine all parts
    png_data = png_signature + ihdr_chunk + idat_chunk + iend_chunk
    
    return png_data

def create_texture_file():
    """Create the texture file"""
    try:
        png_data = create_simple_png()
        
        # Write to file
        with open('src/main/resources/assets/mob-a-la-con/textures/entity/shadow_phantom.png', 'wb') as f:
            f.write(png_data)
        
        print("Simple nature texture created successfully!")
        print("Created 8x8 PNG with natural colors")
        print("Colors used: Bark brown, Forest green, Gold eyes, Moss, Leaf green, Golden accents")
        
    except Exception as e:
        print(f"Error creating texture: {e}")
        print("You may need to create the texture manually using an image editor.")

if __name__ == "__main__":
    create_texture_file()
