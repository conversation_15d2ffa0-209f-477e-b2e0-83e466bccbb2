package com.cobblemonrp.client.model;

import com.cobblemonrp.entity.ShadowPhantomEntity;
import net.minecraft.client.model.*;
import net.minecraft.client.render.VertexConsumer;
import net.minecraft.client.render.entity.model.EntityModel;
import net.minecraft.client.util.math.MatrixStack;
import net.minecraft.util.math.MathHelper;

/**
 * ShadowPhantom Entity Model
 * Similar to Phantom but with distinct visual features
 */
public class ShadowPhantomEntityModel extends EntityModel<ShadowPhantomEntity> {
    private final ModelPart body;
    private final ModelPart leftWingBase;
    private final ModelPart leftWingTip;
    private final ModelPart rightWingBase;
    private final ModelPart rightWingTip;
    private final ModelPart head;
    private final ModelPart tail;
    private final ModelPart tail2;

    public ShadowPhantomEntityModel(ModelPart root) {
        this.body = root.getChild("body");
        this.leftWingBase = root.getChild("left_wing_base");
        this.leftWingTip = root.getChild("left_wing_tip");
        this.rightWingBase = root.getChild("right_wing_base");
        this.rightWingTip = root.getChild("right_wing_tip");
        this.head = root.getChild("head");
        this.tail = root.getChild("tail");
        this.tail2 = root.getChild("tail2");
    }

    public static TexturedModelData getTexturedModelData() {
        ModelData modelData = new ModelData();
        ModelPartData modelPartData = modelData.getRoot();

        // Body - slightly larger than Phantom
        modelPartData.addChild("body", 
            ModelPartBuilder.create()
                .uv(0, 8)
                .cuboid(-3.0F, -2.0F, -8.0F, 5.0F, 3.0F, 9.0F), 
            ModelTransform.of(0.0F, 1.0F, 0.0F, 0.1F, 0.0F, 0.0F));

        // Head - more angular than Phantom
        modelPartData.addChild("head", 
            ModelPartBuilder.create()
                .uv(0, 0)
                .cuboid(-4.0F, -2.0F, -5.0F, 7.0F, 3.0F, 5.0F), 
            ModelTransform.of(0.0F, 1.0F, -7.0F, 0.25F, 0.0F, 0.0F));

        // Left wing base - darker, more tattered appearance
        modelPartData.addChild("left_wing_base", 
            ModelPartBuilder.create()
                .uv(23, 12)
                .cuboid(0.0F, 0.0F, 0.0F, 6.0F, 2.0F, 9.0F), 
            ModelTransform.of(2.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F));

        // Left wing tip
        modelPartData.addChild("left_wing_tip", 
            ModelPartBuilder.create()
                .uv(16, 24)
                .cuboid(0.0F, 0.0F, 0.0F, 13.0F, 1.0F, 9.0F), 
            ModelTransform.of(6.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F));

        // Right wing base
        modelPartData.addChild("right_wing_base", 
            ModelPartBuilder.create()
                .uv(23, 12)
                .cuboid(-6.0F, 0.0F, 0.0F, 6.0F, 2.0F, 9.0F), 
            ModelTransform.of(-2.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F));

        // Right wing tip
        modelPartData.addChild("right_wing_tip", 
            ModelPartBuilder.create()
                .uv(16, 24)
                .cuboid(-13.0F, 0.0F, 0.0F, 13.0F, 1.0F, 9.0F), 
            ModelTransform.of(-6.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F));

        // Tail - longer and more menacing
        modelPartData.addChild("tail", 
            ModelPartBuilder.create()
                .uv(3, 20)
                .cuboid(-2.0F, 0.0F, 0.0F, 3.0F, 2.0F, 6.0F), 
            ModelTransform.of(0.0F, 1.0F, 1.0F, -0.05F, 0.0F, 0.0F));

        // Tail tip
        modelPartData.addChild("tail2", 
            ModelPartBuilder.create()
                .uv(4, 29)
                .cuboid(-1.0F, 0.0F, 0.0F, 1.0F, 1.0F, 6.0F), 
            ModelTransform.of(0.0F, 0.5F, 6.0F, -0.05F, 0.0F, 0.0F));

        return TexturedModelData.of(modelData, 64, 64);
    }

    @Override
    public void setAngles(ShadowPhantomEntity entity, float limbAngle, float limbDistance, float animationProgress, float headYaw, float headPitch) {
        // Wing flapping animation
        float wingFlap = MathHelper.cos(animationProgress * 0.3F) * 0.4F;
        
        // Left wing animation
        this.leftWingBase.roll = -0.4F + wingFlap;
        this.leftWingTip.roll = -0.8F + wingFlap * 1.5F;
        
        // Right wing animation (mirrored)
        this.rightWingBase.roll = 0.4F - wingFlap;
        this.rightWingTip.roll = 0.8F - wingFlap * 1.5F;

        // Head movement
        this.head.pitch = headPitch * 0.017453292F;
        this.head.yaw = headYaw * 0.017453292F;

        // Tail swaying
        float tailSway = MathHelper.sin(animationProgress * 0.1F) * 0.1F;
        this.tail.yaw = tailSway;
        this.tail2.yaw = tailSway * 1.5F;

        // Body bobbing
        this.body.pitch = 0.1F + MathHelper.sin(animationProgress * 0.2F) * 0.05F;
    }

    @Override
    public void render(MatrixStack matrices, VertexConsumer vertices, int light, int overlay, float red, float green, float blue, float alpha) {
        // Render all parts
        this.body.render(matrices, vertices, light, overlay, red, green, blue, alpha);
        this.head.render(matrices, vertices, light, overlay, red, green, blue, alpha);
        this.leftWingBase.render(matrices, vertices, light, overlay, red, green, blue, alpha);
        this.leftWingTip.render(matrices, vertices, light, overlay, red, green, blue, alpha);
        this.rightWingBase.render(matrices, vertices, light, overlay, red, green, blue, alpha);
        this.rightWingTip.render(matrices, vertices, light, overlay, red, green, blue, alpha);
        this.tail.render(matrices, vertices, light, overlay, red, green, blue, alpha);
        this.tail2.render(matrices, vertices, light, overlay, red, green, blue, alpha);
    }
}
