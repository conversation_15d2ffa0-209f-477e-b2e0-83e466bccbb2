# ShadowPhantom Texture Instructions

## Required Texture File
Create a file named `shadow_phantom.png` in this directory with the following specifications:

### Texture Specifications:
- **Size**: 64x64 pixels
- **Format**: PNG with transparency support
- **Style**: Dark, ethereal phantom-like creature

### Design Guidelines:
1. **Base Color**: Dark purple/black with subtle blue highlights
2. **Wings**: Tattered, translucent appearance with darker edges
3. **Body**: Sleek, elongated form similar to Phantom but more angular
4. **Eyes**: Glowing blue or purple eyes
5. **Transparency**: Use alpha channel for translucent wing membranes

### UV Mapping (based on model):
- Body: UV coordinates (0, 8) - 5x3x9 pixels
- Head: UV coordinates (0, 0) - 7x3x5 pixels  
- Wing Base: UV coordinates (23, 12) - 6x2x9 pixels
- Wing Tip: UV coordinates (16, 24) - 13x1x9 pixels
- Tail: UV coordinates (3, 20) - 3x2x6 pixels
- Tail2: UV coordinates (4, 29) - 1x1x6 pixels

### Recommended Tools:
- GIMP (free)
- Paint.NET (free)
- Photoshop
- Aseprite (for pixel art)

### Color Palette Suggestions:
- Primary: #2D1B69 (dark purple)
- Secondary: #1A0F3A (darker purple)
- Accent: #4A90E2 (blue glow)
- Highlight: #6B46C1 (lighter purple)
- Shadow: #0F0A1E (very dark purple/black)

The texture should make the ShadowPhantom visually distinct from the vanilla Phantom while maintaining a similar ethereal, threatening appearance.
