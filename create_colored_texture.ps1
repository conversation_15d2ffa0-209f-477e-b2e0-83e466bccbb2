# Create a colored nature texture using PowerShell and .NET
Add-Type -AssemblyName System.Drawing

Write-Host "Creating nature-themed colored texture..." -ForegroundColor Green

try {
    # Create a 64x64 bitmap
    $bitmap = New-Object System.Drawing.Bitmap(64, 64)
    $graphics = [System.Drawing.Graphics]::FromImage($bitmap)
    
    # Define nature colors
    $barkBrown = [System.Drawing.Color]::FromArgb(139, 69, 19)      # #8B4513
    $forestGreen = [System.Drawing.Color]::FromArgb(45, 80, 22)     # #2D5016
    $goldEyes = [System.Drawing.Color]::FromArgb(255, 215, 0)       # #FFD700
    $mossGreen = [System.Drawing.Color]::FromArgb(154, 205, 50)     # #9ACD32
    $leafGreen = [System.Drawing.Color]::FromArgb(34, 139, 34)      # #228B22
    $goldenAccent = [System.Drawing.Color]::FromArgb(212, 175, 55)  # #D4AF37
    $darkGreen = [System.Drawing.Color]::FromArgb(26, 48, 9)        # #1A3009
    $sageGreen = [System.Drawing.Color]::FromArgb(143, 188, 143)    # #8FBC8F
    
    # Create brushes
    $barkBrush = New-Object System.Drawing.SolidBrush($barkBrown)
    $forestBrush = New-Object System.Drawing.SolidBrush($forestGreen)
    $goldBrush = New-Object System.Drawing.SolidBrush($goldEyes)
    $mossBrush = New-Object System.Drawing.SolidBrush($mossGreen)
    $leafBrush = New-Object System.Drawing.SolidBrush($leafGreen)
    $goldenBrush = New-Object System.Drawing.SolidBrush($goldenAccent)
    $darkBrush = New-Object System.Drawing.SolidBrush($darkGreen)
    $sageBrush = New-Object System.Drawing.SolidBrush($sageGreen)
    
    # Fill background with forest green
    $graphics.FillRectangle($forestBrush, 0, 0, 64, 64)
    
    # Create bark brown border
    $graphics.FillRectangle($barkBrush, 0, 0, 64, 4)      # Top
    $graphics.FillRectangle($barkBrush, 0, 60, 64, 4)     # Bottom
    $graphics.FillRectangle($barkBrush, 0, 0, 4, 64)      # Left
    $graphics.FillRectangle($barkBrush, 60, 0, 4, 64)     # Right
    
    # Add gold eyes (top area)
    $graphics.FillRectangle($goldBrush, 12, 8, 8, 8)      # Left eye
    $graphics.FillRectangle($goldBrush, 44, 8, 8, 8)      # Right eye
    
    # Add moss details (middle area)
    $graphics.FillRectangle($mossBrush, 8, 20, 48, 4)     # Moss stripe
    $graphics.FillRectangle($mossBrush, 12, 28, 40, 4)    # Another moss stripe
    
    # Add leaf green wing details
    $graphics.FillRectangle($leafBrush, 4, 36, 56, 8)     # Wing area 1
    $graphics.FillRectangle($leafBrush, 8, 48, 48, 6)     # Wing area 2
    
    # Add golden accents
    $graphics.FillRectangle($goldenBrush, 16, 24, 32, 2)  # Golden stripe
    $graphics.FillRectangle($goldenBrush, 20, 52, 24, 2)  # Golden accent
    
    # Add some dark green shadows
    $graphics.FillRectangle($darkBrush, 4, 4, 2, 56)      # Left shadow
    $graphics.FillRectangle($darkBrush, 58, 4, 2, 56)     # Right shadow
    
    # Add sage green highlights
    $graphics.FillRectangle($sageBrush, 24, 32, 16, 2)    # Center highlight
    
    # Save as PNG
    $outputPath = "src\main\resources\assets\mob-a-la-con\textures\entity\shadow_phantom.png"
    $bitmap.Save($outputPath, [System.Drawing.Imaging.ImageFormat]::Png)
    
    Write-Host "Colored nature texture created successfully!" -ForegroundColor Green
    Write-Host "Saved to: $outputPath" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "Colors used:" -ForegroundColor Yellow
    Write-Host "  Bark Brown (#8B4513) - Border and structure" -ForegroundColor DarkYellow
    Write-Host "  Forest Green (#2D5016) - Main body color" -ForegroundColor Green
    Write-Host "  Gold (#FFD700) - Eyes and highlights" -ForegroundColor Yellow
    Write-Host "  Moss Green (#9ACD32) - Natural details" -ForegroundColor Green
    Write-Host "  Leaf Green (#228B22) - Wing details" -ForegroundColor Green
    Write-Host "  Golden (#D4AF37) - Accent colors" -ForegroundColor Yellow
    Write-Host "  Dark Green (#1A3009) - Shadows" -ForegroundColor DarkGreen
    Write-Host "  Sage Green (#8FBC8F) - Highlights" -ForegroundColor Green
    
    # Cleanup
    $graphics.Dispose()
    $bitmap.Dispose()
    $barkBrush.Dispose()
    $forestBrush.Dispose()
    $goldBrush.Dispose()
    $mossBrush.Dispose()
    $leafBrush.Dispose()
    $goldenBrush.Dispose()
    $darkBrush.Dispose()
    $sageBrush.Dispose()
    
} catch {
    Write-Host "Error creating texture: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Please check if the output directory exists." -ForegroundColor Yellow
}
