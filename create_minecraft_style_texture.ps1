# Create a Minecraft-style Nature Phantom texture using PowerShell and .NET
Add-Type -AssemblyName System.Drawing

Write-Host "Creating Minecraft-style Nature Phantom texture..." -ForegroundColor Green

try {
    # Create a 64x64 bitmap
    $bitmap = New-Object System.Drawing.Bitmap(64, 64)
    $graphics = [System.Drawing.Graphics]::FromImage($bitmap)
    
    # Set pixel-perfect rendering
    $graphics.InterpolationMode = [System.Drawing.Drawing2D.InterpolationMode]::NearestNeighbor
    $graphics.PixelOffsetMode = [System.Drawing.Drawing2D.PixelOffsetMode]::Half
    
    # Define nature colors (Minecraft style palette)
    $forestGreen = [System.Drawing.Color]::FromArgb(45, 80, 22)        # #2D5016 - Main body
    $darkGreen = [System.Drawing.Color]::FromArgb(26, 48, 9)           # #1A3009 - Shadows
    $goldenYellow = [System.Drawing.Color]::FromArgb(212, 175, 55)     # #D4AF37 - Accents
    $sageGreen = [System.Drawing.Color]::FromArgb(143, 188, 143)       # #8FBC8F - Highlights
    $veryDarkGreen = [System.Drawing.Color]::FromArgb(15, 26, 10)      # #0F1A0A - Deep shadows
    $barkBrown = [System.Drawing.Color]::FromArgb(139, 69, 19)         # #8B4513 - Bark details
    $mossGreen = [System.Drawing.Color]::FromArgb(154, 205, 50)        # #9ACD32 - Moss
    $goldGlow = [System.Drawing.Color]::FromArgb(255, 215, 0)          # #FFD700 - Eyes
    $transparent = [System.Drawing.Color]::FromArgb(0, 0, 0, 0)        # Transparent
    
    # Create brushes
    $forestBrush = New-Object System.Drawing.SolidBrush($forestGreen)
    $darkBrush = New-Object System.Drawing.SolidBrush($darkGreen)
    $goldenBrush = New-Object System.Drawing.SolidBrush($goldenYellow)
    $sageBrush = New-Object System.Drawing.SolidBrush($sageGreen)
    $veryDarkBrush = New-Object System.Drawing.SolidBrush($veryDarkGreen)
    $barkBrush = New-Object System.Drawing.SolidBrush($barkBrown)
    $mossBrush = New-Object System.Drawing.SolidBrush($mossGreen)
    $glowBrush = New-Object System.Drawing.SolidBrush($goldGlow)
    
    # Clear background (transparent)
    $graphics.Clear($transparent)
    
    # === HEAD SECTION (UV: 0,0 - 7x3x5) ===
    # Head top (0,0 to 7,5)
    $graphics.FillRectangle($forestBrush, 0, 0, 7, 5)
    $graphics.FillRectangle($darkBrush, 0, 0, 1, 5)      # Left edge
    $graphics.FillRectangle($darkBrush, 6, 0, 1, 5)      # Right edge
    $graphics.FillRectangle($darkBrush, 0, 0, 7, 1)      # Top edge
    $graphics.FillRectangle($darkBrush, 0, 4, 7, 1)      # Bottom edge
    
    # Head front (7,0 to 12,5)
    $graphics.FillRectangle($forestBrush, 7, 0, 5, 5)
    $graphics.FillRectangle($glowBrush, 8, 1, 1, 1)      # Left eye
    $graphics.FillRectangle($glowBrush, 10, 1, 1, 1)     # Right eye
    $graphics.FillRectangle($darkBrush, 7, 0, 5, 1)      # Top
    $graphics.FillRectangle($darkBrush, 7, 4, 5, 1)      # Bottom
    $graphics.FillRectangle($darkBrush, 7, 0, 1, 5)      # Left
    $graphics.FillRectangle($darkBrush, 11, 0, 1, 5)     # Right
    
    # Head right (12,0 to 19,5)
    $graphics.FillRectangle($forestBrush, 12, 0, 7, 5)
    $graphics.FillRectangle($darkBrush, 12, 0, 1, 5)     # Left edge
    $graphics.FillRectangle($darkBrush, 18, 0, 1, 5)     # Right edge
    
    # Head left (19,0 to 26,5)
    $graphics.FillRectangle($forestBrush, 19, 0, 7, 5)
    $graphics.FillRectangle($darkBrush, 19, 0, 1, 5)     # Left edge
    $graphics.FillRectangle($darkBrush, 25, 0, 1, 5)     # Right edge
    
    # Head back (26,0 to 31,5)
    $graphics.FillRectangle($darkGreen, 26, 0, 5, 5)
    $graphics.FillRectangle($veryDarkBrush, 26, 0, 5, 1) # Top
    $graphics.FillRectangle($veryDarkBrush, 26, 4, 5, 1) # Bottom
    
    # Head bottom (31,0 to 38,5)
    $graphics.FillRectangle($sageGreen, 31, 0, 7, 5)
    
    # === BODY SECTION (UV: 0,8 - 5x3x9) ===
    # Body top (0,8 to 9,11)
    $graphics.FillRectangle($forestBrush, 0, 8, 9, 3)
    $graphics.FillRectangle($mossBrush, 1, 8, 7, 1)      # Moss stripe
    $graphics.FillRectangle($darkBrush, 0, 8, 1, 3)      # Left edge
    $graphics.FillRectangle($darkBrush, 8, 8, 1, 3)      # Right edge
    
    # Body front (9,8 to 14,11)
    $graphics.FillRectangle($forestBrush, 9, 8, 5, 3)
    $graphics.FillRectangle($barkBrush, 10, 9, 3, 1)     # Bark detail
    $graphics.FillRectangle($darkBrush, 9, 8, 1, 3)      # Left
    $graphics.FillRectangle($darkBrush, 13, 8, 1, 3)     # Right
    
    # Body right (14,8 to 23,11)
    $graphics.FillRectangle($forestBrush, 14, 8, 9, 3)
    $graphics.FillRectangle($darkBrush, 14, 8, 1, 3)     # Left edge
    $graphics.FillRectangle($darkBrush, 22, 8, 1, 3)     # Right edge
    
    # Body left (23,8 to 32,11)
    $graphics.FillRectangle($forestBrush, 23, 8, 9, 3)
    $graphics.FillRectangle($darkBrush, 23, 8, 1, 3)     # Left edge
    $graphics.FillRectangle($darkBrush, 31, 8, 1, 3)     # Right edge
    
    # Body back (32,8 to 37,11)
    $graphics.FillRectangle($darkBrush, 32, 8, 5, 3)
    
    # Body bottom (37,8 to 46,11)
    $graphics.FillRectangle($sageGreen, 37, 8, 9, 3)
    
    # === WING BASE SECTION (UV: 23,12 - 6x2x9) ===
    # Wing base top (23,12 to 32,14)
    $graphics.FillRectangle($forestBrush, 23, 12, 9, 2)
    $graphics.FillRectangle($goldenBrush, 24, 12, 7, 1)  # Golden accent
    $graphics.FillRectangle($darkBrush, 23, 12, 1, 2)    # Left edge
    $graphics.FillRectangle($darkBrush, 31, 12, 1, 2)    # Right edge
    
    # Wing base front (32,12 to 38,14)
    $graphics.FillRectangle($forestBrush, 32, 12, 6, 2)
    $graphics.FillRectangle($darkBrush, 32, 12, 1, 2)    # Left
    $graphics.FillRectangle($darkBrush, 37, 12, 1, 2)    # Right
    
    # === WING TIP SECTION (UV: 16,24 - 13x1x9) ===
    # Wing tip (16,24 to 29,25)
    $graphics.FillRectangle($sageGreen, 16, 24, 13, 1)
    $graphics.FillRectangle($goldenBrush, 17, 24, 1, 1)  # Golden tip
    $graphics.FillRectangle($goldenBrush, 19, 24, 1, 1)  # Golden tip
    $graphics.FillRectangle($goldenBrush, 21, 24, 1, 1)  # Golden tip
    $graphics.FillRectangle($goldenBrush, 23, 24, 1, 1)  # Golden tip
    $graphics.FillRectangle($goldenBrush, 25, 24, 1, 1)  # Golden tip
    $graphics.FillRectangle($goldenBrush, 27, 24, 1, 1)  # Golden tip
    
    # === TAIL SECTIONS ===
    # Tail (3,20 to 9,22)
    $graphics.FillRectangle($forestBrush, 3, 20, 6, 2)
    $graphics.FillRectangle($darkBrush, 3, 20, 1, 2)     # Left edge
    $graphics.FillRectangle($darkBrush, 8, 20, 1, 2)     # Right edge
    $graphics.FillRectangle($mossBrush, 4, 20, 4, 1)     # Moss detail
    
    # Tail2 (4,29 to 10,30)
    $graphics.FillRectangle($darkBrush, 4, 29, 6, 1)
    $graphics.FillRectangle($goldenBrush, 5, 29, 1, 1)   # Golden accent
    $graphics.FillRectangle($goldenBrush, 7, 29, 1, 1)   # Golden accent
    $graphics.FillRectangle($goldenBrush, 9, 29, 1, 1)   # Golden accent
    
    # === ADDITIONAL WING DETAILS ===
    # Left wing membrane (0,16 to 15,23)
    for ($x = 0; $x -lt 16; $x++) {
        for ($y = 16; $y -lt 24; $y++) {
            if (($x + $y) % 3 -eq 0) {
                $graphics.FillRectangle($sageBrush, $x, $y, 1, 1)
            } elseif (($x + $y) % 4 -eq 0) {
                $graphics.FillRectangle($goldenBrush, $x, $y, 1, 1)
            } else {
                $graphics.FillRectangle($forestBrush, $x, $y, 1, 1)
            }
        }
    }
    
    # Right wing membrane (32,16 to 47,23)
    for ($x = 32; $x -lt 48; $x++) {
        for ($y = 16; $y -lt 24; $y++) {
            if (($x + $y) % 3 -eq 0) {
                $graphics.FillRectangle($sageBrush, $x, $y, 1, 1)
            } elseif (($x + $y) % 4 -eq 0) {
                $graphics.FillRectangle($goldenBrush, $x, $y, 1, 1)
            } else {
                $graphics.FillRectangle($forestBrush, $x, $y, 1, 1)
            }
        }
    }
    
    # Save as PNG
    $outputPath = "src\main\resources\assets\mob-a-la-con\textures\entity\shadow_phantom.png"
    $bitmap.Save($outputPath, [System.Drawing.Imaging.ImageFormat]::Png)
    
    Write-Host "Minecraft-style Nature Phantom texture created successfully!" -ForegroundColor Green
    Write-Host "Saved to: $outputPath" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "Features:" -ForegroundColor Yellow
    Write-Host "  - Proper Phantom UV mapping" -ForegroundColor Green
    Write-Host "  - Minecraft pixel art style" -ForegroundColor Green
    Write-Host "  - Nature color palette" -ForegroundColor Green
    Write-Host "  - Golden glowing eyes" -ForegroundColor Yellow
    Write-Host "  - Moss and bark details" -ForegroundColor Green
    Write-Host "  - Leaf-pattern wing membranes" -ForegroundColor Green
    
    # Cleanup
    $graphics.Dispose()
    $bitmap.Dispose()
    $forestBrush.Dispose()
    $darkBrush.Dispose()
    $goldenBrush.Dispose()
    $sageBrush.Dispose()
    $veryDarkBrush.Dispose()
    $barkBrush.Dispose()
    $mossBrush.Dispose()
    $glowBrush.Dispose()
    
} catch {
    Write-Host "Error creating texture: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Please check if the output directory exists." -ForegroundColor Yellow
}
