package com.cobblemonrp.client;

import com.cobblemonrp.MobALaCon;
import com.cobblemonrp.client.model.ShadowPhantomEntityModel;
import com.cobblemonrp.client.renderer.ShadowPhantomEntityRenderer;
import net.fabricmc.api.ClientModInitializer;
import net.fabricmc.api.EnvType;
import net.fabricmc.api.Environment;
import net.fabricmc.fabric.api.client.rendering.v1.EntityModelLayerRegistry;
import net.fabricmc.fabric.api.client.rendering.v1.EntityRendererRegistry;
import net.minecraft.client.render.entity.model.EntityModelLayer;
import net.minecraft.util.Identifier;

@Environment(EnvType.CLIENT)
public class MobALaConClient implements ClientModInitializer {
    
    public static final EntityModelLayer SHADOW_PHANTOM_LAYER = new EntityModelLayer(
        new Identifier(MobALaCon.MOD_ID, "shadow_phantom"), "main");

    @Override
    public void onInitializeClient() {
        // Register entity renderer
        EntityRendererRegistry.register(MobALaCon.SHADOW_PHANTOM, ShadowPhantomEntityRenderer::new);
        
        // Register model layer
        EntityModelLayerRegistry.registerModelLayer(SHADOW_PHANTOM_LAYER, ShadowPhantomEntityModel::getTexturedModelData);
        
        MobALaCon.LOGGER.info("Mob A La Con client initialized!");
    }
}
